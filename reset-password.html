<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta name="description" content="Reset Password - <PERSON><PERSON>oor">
  
  <title>Zahoor - Reset Password</title>
  
  <link rel="icon" type="image/png" href="favicon.png"/>
  <link rel="stylesheet" href="styles.css">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;600&display=swap" rel="stylesheet">
</head>
<body>
  <div class="auth-container">
    <form class="auth-form" id="reset-password-form" autocomplete="off">
      <h2>Reset Password</h2>
      <p style="text-align: center; color: #666; margin-bottom: 1.5rem;">
        Enter your email address and we'll send you a link to reset your password.
      </p>
      <input type="email" id="reset-email" placeholder="Email" required>
      <button type="submit">Send Reset Link</button>
      <p>Remember your password? <a href="signin.html">Sign In</a></p>
    </form>
  </div>

  <!-- Supabase -->
  <script src="https://unpkg.com/@supabase/supabase-js@2"></script>
  
  <!-- Include the authentication scripts -->
  <script src="supabase-config.js"></script>
  <script>
    document.addEventListener('DOMContentLoaded', function() {
      setTimeout(() => {
        setupResetPasswordForm();
      }, 100);
    });

    function setupResetPasswordForm() {
      const resetForm = document.getElementById('reset-password-form');
      if (resetForm) {
        resetForm.addEventListener('submit', async function(event) {
          event.preventDefault();
          
          const email = document.getElementById('reset-email').value;
          const submitBtn = this.querySelector('button[type="submit"]');
          const originalText = submitBtn.textContent;

          // Validate email
          const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
          if (!emailRegex.test(email)) {
            showError('Please enter a valid email address.');
            return;
          }

          showLoading(submitBtn, 'Sending...');
          hideError();

          try {
            const result = await window.authService.resetPassword(email);
            
            if (result.success) {
              showSuccess('Password reset link sent! Check your email.');
              this.reset();
            } else {
              showError(result.error || 'Failed to send reset link. Please try again.');
            }
          } catch (error) {
            showError('An unexpected error occurred. Please try again.');
          } finally {
            hideLoading(submitBtn, originalText);
          }
        });
      }
    }

    // Helper functions (duplicated for this standalone page)
    function showLoading(button, text = 'Loading...') {
      button.disabled = true;
      button.textContent = text;
    }

    function hideLoading(button, originalText) {
      button.disabled = false;
      button.textContent = originalText;
    }

    function showError(message) {
      let errorDiv = document.querySelector('.auth-error');
      if (!errorDiv) {
        errorDiv = document.createElement('div');
        errorDiv.className = 'auth-error';
        errorDiv.style.cssText = `
          background: #fee2e2;
          color: #dc2626;
          padding: 0.75rem;
          border-radius: 8px;
          margin-bottom: 1rem;
          border: 1px solid #fecaca;
          font-size: 0.9rem;
        `;
        const form = document.querySelector('.auth-form');
        if (form) {
          form.insertBefore(errorDiv, form.firstChild);
        }
      }
      errorDiv.textContent = message;
      errorDiv.style.display = 'block';
    }

    function hideError() {
      const errorDiv = document.querySelector('.auth-error');
      if (errorDiv) {
        errorDiv.style.display = 'none';
      }
    }

    function showSuccess(message) {
      let successDiv = document.querySelector('.auth-success');
      if (!successDiv) {
        successDiv = document.createElement('div');
        successDiv.className = 'auth-success';
        successDiv.style.cssText = `
          background: #dcfce7;
          color: #16a34a;
          padding: 0.75rem;
          border-radius: 8px;
          margin-bottom: 1rem;
          border: 1px solid #bbf7d0;
          font-size: 0.9rem;
        `;
        const form = document.querySelector('.auth-form');
        if (form) {
          form.insertBefore(successDiv, form.firstChild);
        }
      }
      successDiv.textContent = message;
      successDiv.style.display = 'block';
    }
  </script>
</body>
</html>
