body {
  font-family: 'Poppins', sans-serif;
  background: linear-gradient(135deg, #f8fafc 0%, #e0e7ef 100%);
  color: #222;
  margin: 0;
  padding: 0;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

header {
  background: #fff;
  box-shadow: 0 2px 20px rgba(0,0,0,0.08);
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 2.5rem;
  border-radius: 0 0 24px 24px;
  position: sticky;
  top: 0;
  z-index: 1000;
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(59,130,246,0.1);
}

.logo {
  flex-shrink: 0;
  display: flex;
  align-items: center;
}

.logo-image {
  height: 95px;
  width: auto;
  max-width: 200px;
  object-fit: contain;
  transition: transform 0.3s ease;
}

.logo-image:hover {
  transform: scale(1.05);
}

nav {
  flex: 1;
  display: flex;
  justify-content: center;
  margin: 0 2rem;
}

nav ul {
  list-style: none;
  display: flex;
  gap: 0.5rem;
  margin: 0;
  padding: 0;
  background: rgba(59,130,246,0.05);
  border-radius: 16px;
  padding: 0.5rem;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(59,130,246,0.1);
}

nav li {
  position: relative;
}

nav a {
  text-decoration: none;
  color: #4a5568;
  font-weight: 500;
  font-size: 0.95rem;
  transition: all 0.3s cubic-bezier(.4,0,.2,1);
  padding: 0.75rem 1.25rem;
  border-radius: 12px;
  position: relative;
  display: block;
  white-space: nowrap;
}

nav a:hover {
  background: rgba(59,130,246,0.1);
  color: #3b82f6;
  transform: translateY(-1px);
}

nav a.active {
  background: #3b82f6;
  color: #fff;
  box-shadow: 0 4px 12px rgba(59,130,246,0.3);
  transform: translateY(-1px);
}

/* Special styling for Sign In/Sign Up buttons */
nav a[href*="signin"], nav a[href*="signup"] {
  background: linear-gradient(135deg, #3b82f6, #60a5fa);
  color: #fff;
  font-weight: 600;
  margin-left: 0.5rem;
  box-shadow: 0 2px 8px rgba(59,130,246,0.2);
}

nav a[href*="signin"]:hover, nav a[href*="signup"]:hover {
  background: linear-gradient(135deg, #2563eb, #3b82f6);
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(59,130,246,0.3);
}

.hero {
  background: linear-gradient(120deg, #3b82f6 0%, #60a5fa 100%);
  color: #fff;
  padding: 4rem 2rem 3rem 2rem;
  border-radius: 24px;
  margin: 2rem 0;
  text-align: center;
  box-shadow: 0 4px 24px rgba(59,130,246,0.08);
}

.hero-content h2 {
  font-size: 2.8rem;
  font-weight: 700;
  margin-bottom: 1rem;
}

.hero-content p {
  font-size: 1.3rem;
  margin-bottom: 2rem;
}

.btn {
  padding: 0.8rem 2rem;
  border-radius: 8px;
  font-weight: 600;
  border: none;
  cursor: pointer;
  text-decoration: none;
  transition: background 0.2s, color 0.2s;
}

.primary-btn {
  background: #3b82f6;
  color: #fff;
}

.primary-btn:hover {
  background: #2563eb;
}

.secondary-btn {
  background: #fff;
  color: #3b82f6;
  border: 2px solid #3b82f6;
}

.secondary-btn:hover {
  background: #3b82f6;
  color: #fff;
}

.categories-grid, .products-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
  gap: 2rem;
  margin: 2rem 0;
}

.category-card, .product-card {
  background: #fff;
  border-radius: 16px;
  box-shadow: 0 2px 12px rgba(0,0,0,0.06);
  padding: 1.5rem;
  text-align: center;
  transition: transform 0.2s;
}

.category-card:hover, .product-card:hover {
  transform: translateY(-8px) scale(1.03);
}

.category-card img, .product-card img {
  width: 100%;
  border-radius: 12px;
  margin-bottom: 1rem;
}

.section-title {
  font-size: 2rem;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 1.5rem;
  text-align: center;
}

footer {
  background: #f1f5f9;
  padding: 2rem 0 1rem 0;
  border-radius: 24px 24px 0 0;
  margin-top: 3rem;
  box-shadow: 0 -2px 12px rgba(0,0,0,0.04);
}

.footer-content {
  display: flex;
  justify-content: center;
  gap: 3rem;
  flex-wrap: wrap;
}

.footer-column h3 {
  color: #3b82f6;
  margin-bottom: 1rem;
}

.footer-bottom {
  text-align: center;
  margin-top: 1rem;
  color: #888;
  font-size: 0.95rem;
}

/* Modal Styles */
.modal {
  display: none;
  position: fixed;
  z-index: 1000;
  left: 0; top: 0;
  width: 100vw; height: 100vh;
  background: rgba(0,0,0,0.25);
  justify-content: center;
  align-items: center;
}

.modal-content {
  background: #fff;
  padding: 2.5rem 2rem;
  border-radius: 18px;
  box-shadow: 0 8px 32px rgba(59,130,246,0.12);
  text-align: center;
  max-width: 350px;
  width: 90%;
  position: relative;
}

.close-btn {
  position: absolute;
  top: 1rem; right: 1rem;
  font-size: 1.5rem;
  color: #888;
  cursor: pointer;
}

.modal-actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
  margin-top: 2rem;
}

/* Auth Styles */
.auth-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(120deg, #3b82f6 0%, #60a5fa 100%);
}

.auth-form {
  background: #fff;
  padding: 2.5rem 2rem;
  border-radius: 18px;
  box-shadow: 0 8px 32px rgba(59,130,246,0.12);
  display: flex;
  flex-direction: column;
  gap: 1.2rem;
  min-width: 340px;
  max-width: 360px;
  width: 100%;
}

.auth-form h2 {
  margin-bottom: 1rem;
  color: #3b82f6;
  font-size: 2rem;
  font-weight: 700;
  text-align: center;
}

.auth-form input {
  padding: 0.9rem 1rem;
  border-radius: 8px;
  border: 1.5px solid #e5e7eb;
  font-size: 1rem;
  outline: none;
  transition: border 0.2s;
  background: #f8fafc;
}

.auth-form input:focus {
  border: 1.5px solid #3b82f6;
  background: #fff;
}

.auth-form button {
  padding: 0.9rem 0;
  border-radius: 8px;
  font-weight: 600;
  border: none;
  background: #3b82f6;
  color: #fff;
  font-size: 1.1rem;
  cursor: pointer;
  transition: background 0.2s;
}

.auth-form button:hover {
  background: #2563eb;
}

.auth-form p {
  text-align: center;
  margin-top: 1rem;
  font-size: 1rem;
}

.auth-form a {
  color: #7c3aed;
  text-decoration: underline;
  font-weight: 500;
  transition: color 0.2s;
}

.auth-form a:hover {
  color: #3b82f6;
}

/* Auth error and success messages */
.auth-error, .auth-success {
  border-radius: 8px;
  padding: 0.75rem;
  margin-bottom: 1rem;
  font-size: 0.9rem;
  font-weight: 500;
}

.auth-error {
  background: #fee2e2;
  color: #dc2626;
  border: 1px solid #fecaca;
}

.auth-success {
  background: #dcfce7;
  color: #16a34a;
  border: 1px solid #bbf7d0;
}

/* Loading state for buttons */
.auth-form button:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

#profile-box {
  display: flex;
  align-items: center;
  gap: 0.7rem;
  margin-left: 1.5rem;
}
.profile-info {
  display: flex;
  align-items: center;
  gap: 0.7rem;
  background: #f3f6fa;
  padding: 0.4rem 1rem;
  border-radius: 2rem;
  box-shadow: 0 2px 8px rgba(59,130,246,0.06);
}
.profile-img {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid #3b82f6;
  transition: opacity 0.3s ease;
}
.profile-name {
  font-weight: 600;
  color: #222;
  transition: opacity 0.3s ease;
}

/* Loading state for profile */
.profile-info.loading .profile-img,
.profile-info.loading .profile-name {
  opacity: 0.7;
}

/* Smooth fade-in animation */
.profile-info {
  animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(-5px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Add smooth entrance animation to navigation */
nav ul {
  animation: slideIn 0.6s ease-out;
}

nav li {
  animation: fadeIn 0.4s ease-out forwards;
  animation-delay: calc(var(--i) * 0.1s);
}

/* Add hover effect for better interactivity */
nav a::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(59,130,246,0.1), rgba(96,165,250,0.1));
  border-radius: 12px;
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: -1;
}

nav a:hover::before {
  opacity: 1;
}
.logout-btn {
  background: none;
  border: none;
  color: #3b82f6;
  font-size: 1.2rem;
  cursor: pointer;
  margin-left: 0.5rem;
  transition: color 0.2s;
}
.logout-btn:hover {
  color: #ef4444;
}

/* Responsive */
@media (max-width: 768px) {
  header {
    flex-direction: column;
    gap: 1.5rem;
    padding: 1rem 1.5rem;
    position: relative;
  }

  nav {
    margin: 0;
    width: 100%;
  }

  nav ul {
    flex-wrap: wrap;
    justify-content: center;
    gap: 0.3rem;
    padding: 0.3rem;
  }

  nav a {
    padding: 0.6rem 1rem;
    font-size: 0.9rem;
  }

  nav a[href*="signin"], nav a[href*="signup"] {
    margin-left: 0;
    margin-top: 0.3rem;
  }

  .logo-image {
    height: 75px;
    max-width: 165px;
  }

  .categories-grid, .products-grid {
    grid-template-columns: 1fr;
  }
  .hero {
    padding: 2rem 1rem;
  }
}