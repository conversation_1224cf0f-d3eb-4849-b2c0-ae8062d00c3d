<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta name="description" content="Sign Up for Zahoor - Elegant Modest Fashion for Women">
  
  <title><PERSON><PERSON><PERSON> - Sign Up</title>
  
  <link rel="icon" type="image/png" href="favicon.png"/>
  <link rel="stylesheet" href="styles.css">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;600&display=swap" rel="stylesheet">
</head>
<body>
  <div class="auth-container">
    <form class="auth-form" id="signup-form" autocomplete="off">
      <h2>Create Your Account</h2>
      <input type="text" placeholder="Full Name" required>
      <input type="email" placeholder="Email" required>
      <input type="password" placeholder="Password" required>
      <input type="password" placeholder="Confirm Password" required>
      <button type="submit">Sign Up</button>
      <p>Already have an account? <a href="signin.html">Sign In</a></p>
    </form>
  </div>

  <script src="script.js"></script>
  <script>
document.getElementById('signin-form')?.addEventListener('submit', function(e) {
  e.preventDefault();
  // For demo: Save a fake user
  localStorage.setItem('zahoor_user', JSON.stringify({ name: "Zahoor User", img: "https://i.pravatar.cc/40" }));
  window.location.href = "index.html";
});

document.getElementById('signup-form')?.addEventListener('submit', function(e) {
  e.preventDefault();
  const name = this.querySelector('input[type="text"]')?.value || "Zahoor User";
  localStorage.setItem('zahoor_user', JSON.stringify({ name, img: "https://i.pravatar.cc/40" }));
  window.location.href = "index.html";
});
</script>
</body>
</html>