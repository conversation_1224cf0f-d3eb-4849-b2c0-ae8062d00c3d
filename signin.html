<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>Zahoor - Sign In</title>
  <link rel="stylesheet" href="styles.css">
  <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;600&display=swap" rel="stylesheet">
</head>
<body>
  <div class="auth-container">
    <form class="auth-form" id="signin-form" autocomplete="off">
      <h2>Sign In</h2>
      <input type="email" placeholder="Email" required>
      <input type="password" placeholder="Password" required>
      <button type="submit">Sign In</button>
      <p>Don't have an account? <a href="signup.html">Sign Up</a></p>
    </form>
  </div>
  <script>
document.getElementById('signin-form')?.addEventListener('submit', function(e) {
  e.preventDefault();
  // For demo: Save a fake user
  localStorage.setItem('zahoor_user', JSON.stringify({ name: "<PERSON><PERSON><PERSON> User", img: "https://i.pravatar.cc/40" }));
  window.location.href = "index.html";
});

document.getElementById('signup-form')?.addEventListener('submit', function(e) {
  e.preventDefault();
  const name = this.querySelector('input[type="text"]')?.value || "Zahoor User";
  localStorage.setItem('zahoor_user', JSON.stringify({ name, img: "https://i.pravatar.cc/40" }));
  window.location.href = "index.html";
});
</script>
</body>
</html>