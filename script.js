// This file contains JavaScript code to handle interactivity on the clothing website.

// Function to validate email format
function validateEmail(email) {
    const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return re.test(String(email).toLowerCase());
}

// Function to show loading state
function showLoading(button, text = 'Loading...') {
    button.disabled = true;
    button.textContent = text;
}

// Function to hide loading state
function hideLoading(button, originalText) {
    button.disabled = false;
    button.textContent = originalText;
}

// Function to show error message
function showError(message) {
    // Create or update error message element
    let errorDiv = document.querySelector('.auth-error');
    if (!errorDiv) {
        errorDiv = document.createElement('div');
        errorDiv.className = 'auth-error';
        errorDiv.style.cssText = `
            background: #fee2e2;
            color: #dc2626;
            padding: 0.75rem;
            border-radius: 8px;
            margin-bottom: 1rem;
            border: 1px solid #fecaca;
            font-size: 0.9rem;
        `;
        const form = document.querySelector('.auth-form');
        if (form) {
            form.insertBefore(errorDiv, form.firstChild);
        }
    }
    errorDiv.textContent = message;
    errorDiv.style.display = 'block';
}

// Function to hide error message
function hideError() {
    const errorDiv = document.querySelector('.auth-error');
    if (errorDiv) {
        errorDiv.style.display = 'none';
    }
}

// Function to show success message
function showSuccess(message) {
    let successDiv = document.querySelector('.auth-success');
    if (!successDiv) {
        successDiv = document.createElement('div');
        successDiv.className = 'auth-success';
        successDiv.style.cssText = `
            background: #dcfce7;
            color: #16a34a;
            padding: 0.75rem;
            border-radius: 8px;
            margin-bottom: 1rem;
            border: 1px solid #bbf7d0;
            font-size: 0.9rem;
        `;
        const form = document.querySelector('.auth-form');
        if (form) {
            form.insertBefore(successDiv, form.firstChild);
        }
    }
    successDiv.textContent = message;
    successDiv.style.display = 'block';
}

// Wait for auth service to be available and then set up event listeners
document.addEventListener('DOMContentLoaded', function() {
    // Wait a bit for authService to initialize
    setTimeout(() => {
        setupAuthEventListeners();
        setupModalBehavior();
    }, 100);
});

function setupAuthEventListeners() {
    // Handle sign-in form submission
    const signinForm = document.getElementById('signin-form');
    if (signinForm) {
        signinForm.addEventListener('submit', async function(event) {
            event.preventDefault();
            hideError();

            const email = document.getElementById('signin-email').value;
            const password = document.getElementById('signin-password').value;
            const submitBtn = this.querySelector('button[type="submit"]');
            const originalText = submitBtn.textContent;

            if (!validateEmail(email)) {
                showError('Please enter a valid email address.');
                return;
            }

            if (!password) {
                showError('Please enter your password.');
                return;
            }

            showLoading(submitBtn, 'Signing In...');

            try {
                const result = await window.authService.signIn(email, password);

                if (result.success) {
                    showSuccess('Sign in successful! Redirecting...');
                    setTimeout(() => {
                        window.location.href = 'index.html';
                    }, 1500);
                } else {
                    showError(result.error || 'Sign in failed. Please try again.');
                }
            } catch (error) {
                showError('An unexpected error occurred. Please try again.');
            } finally {
                hideLoading(submitBtn, originalText);
            }
        });
    }

    // Handle sign-up form submission
    const signupForm = document.getElementById('signup-form');
    if (signupForm) {
        signupForm.addEventListener('submit', async function(event) {
            event.preventDefault();
            hideError();

            const name = document.getElementById('signup-name').value;
            const email = document.getElementById('signup-email').value;
            const password = document.getElementById('signup-password').value;
            const confirmPassword = document.getElementById('signup-confirm-password').value;
            const submitBtn = this.querySelector('button[type="submit"]');
            const originalText = submitBtn.textContent;

            if (!name.trim()) {
                showError('Please enter your full name.');
                return;
            }

            if (!validateEmail(email)) {
                showError('Please enter a valid email address.');
                return;
            }

            if (password.length < 6) {
                showError('Password must be at least 6 characters long.');
                return;
            }

            if (password !== confirmPassword) {
                showError('Passwords do not match.');
                return;
            }

            showLoading(submitBtn, 'Creating Account...');

            try {
                const result = await window.authService.signUp(email, password, name);

                if (result.success) {
                    showSuccess('Account created successfully! Please check your email to verify your account.');
                    // Clear form
                    this.reset();
                } else {
                    showError(result.error || 'Sign up failed. Please try again.');
                }
            } catch (error) {
                showError('An unexpected error occurred. Please try again.');
            } finally {
                hideLoading(submitBtn, originalText);
            }
        });
    }
}

function setupModalBehavior() {
    // Show modal after 5 seconds if not signed in (only on home page)
    if (window.location.pathname === '/' || window.location.pathname.endsWith('index.html')) {
        setTimeout(() => {
            if (window.authService && !window.authService.isSignedIn()) {
                const modal = document.getElementById('suggest-modal');
                if (modal) {
                    modal.style.display = 'flex';
                }
            }
        }, 5000);
    }

    // Handle modal close
    const closeModal = document.getElementById('close-modal');
    if (closeModal) {
        closeModal.onclick = () => {
            const modal = document.getElementById('suggest-modal');
            if (modal) {
                modal.style.display = 'none';
            }
        };
    }

    // Close modal when clicking outside
    const modal = document.getElementById('suggest-modal');
    if (modal) {
        modal.onclick = (e) => {
            if (e.target === modal) {
                modal.style.display = 'none';
            }
        };
    }
}