// This file contains JavaScript code to handle interactivity on the clothing website.

// Function to validate email format
function validateEmail(email) {
    const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return re.test(String(email).toLowerCase());
}

// Function to handle sign-in form submission
document.getElementById('signin-form').addEventListener('submit', function(event) {
    event.preventDefault();
    const email = document.getElementById('signin-email').value;
    const password = document.getElementById('signin-password').value;

    if (validateEmail(email) && password) {
        // Proceed with sign-in (e.g., send data to server)
        alert('Sign-in successful!');
    } else {
        alert('Please enter a valid email and password.');
    }
});

// Function to handle sign-up form submission
document.getElementById('signup-form').addEventListener('submit', function(event) {
    event.preventDefault();
    const name = document.getElementById('signup-name').value;
    const email = document.getElementById('signup-email').value;
    const password = document.getElementById('signup-password').value;

    if (name && validateEmail(email) && password) {
        // Proceed with sign-up (e.g., send data to server)
        alert('Sign-up successful!');
    } else {
        alert('Please fill in all fields with valid information.');
    }
});

// Function to reset password
document.getElementById('reset-password').addEventListener('click', function() {
    const email = document.getElementById('reset-email').value;
    if (validateEmail(email)) {
        // Proceed with password reset (e.g., send reset link)
        alert('Password reset link sent to your email!');
    } else {
        alert('Please enter a valid email address.');
    }
});

// Simulate user authentication state
function isSignedIn() {
  return localStorage.getItem('zahoor_user');
}

// Show modal after 5 seconds if not signed in
window.addEventListener('DOMContentLoaded', () => {
  if (!isSignedIn()) {
    setTimeout(() => {
      document.getElementById('suggest-modal').style.display = 'flex';
    }, 5000);
  }
  document.getElementById('close-modal').onclick = () => {
    document.getElementById('suggest-modal').style.display = 'none';
  };
});

function showProfile() {
  const user = JSON.parse(localStorage.getItem('zahoor_user'));
  if (user) {
    document.getElementById('profile-box').innerHTML = `
      <div class="profile-info">
        <img src="${user.img}" alt="Profile" class="profile-img">
        <span class="profile-name">${user.name}</span>
        <button id="logout-btn" title="Log out" class="logout-btn"><i class="fa fa-sign-out-alt"></i></button>
      </div>
    `;
    document.getElementById('profile-box').style.display = 'flex';
    document.getElementById('nav-signin').style.display = 'none';
    document.getElementById('nav-signup').style.display = 'none';
    document.getElementById('logout-btn').onclick = () => {
      localStorage.removeItem('zahoor_user');
      window.location.reload();
    };
  }
}

window.addEventListener('DOMContentLoaded', showProfile);