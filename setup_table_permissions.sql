-- S<PERSON> Script to Set Up Table Permissions for "Data of Users"
-- Run this in your Supabase SQL Editor

-- Step 1: Enable Row Level Security (RLS)
ALTER TABLE "Data of Users" ENABLE ROW LEVEL SECURITY;

-- Step 2: Create policy to allow authenticated users to insert their own data
CREATE POLICY "Allow authenticated users to insert" ON "Data of Users"
FOR INSERT TO authenticated
WITH CHECK (true);

-- Step 3: Create policy to allow authenticated users to read their own data
CREATE POLICY "Users can read their own data" ON "Data of Users"
FOR SELECT TO authenticated
USING (true);

-- Step 4: Create policy to allow authenticated users to update their own data (optional)
CREATE POLICY "Users can update their own data" ON "Data of Users"
FOR UPDATE TO authenticated
USING (true)
WITH CHECK (true);

-- Step 5: Grant necessary permissions to authenticated role
GRANT ALL ON "Data of Users" TO authenticated;
GRANT USAGE ON SCHEMA public TO authenticated;

-- Verify the policies were created
SELECT schemaname, tablename, policyname, permissive, roles, cmd, qual, with_check
FROM pg_policies 
WHERE tablename = 'Data of Users';
