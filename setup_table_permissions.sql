-- <PERSON><PERSON> Script to Set Up Table Permissions for "Data of Users"
-- Run this in your Supabase SQL Editor

-- First, let's check if R<PERSON> is already enabled
SELECT schemaname, tablename, rowsecurity
FROM pg_tables
WHERE tablename = 'Data of Users';

-- Drop existing policies if they exist (to avoid conflicts)
DROP POLICY IF EXISTS "Allow authenticated users to insert" ON "Data of Users";
DROP POLICY IF EXISTS "Users can read their own data" ON "Data of Users";
DROP POLICY IF EXISTS "Users can update their own data" ON "Data of Users";

-- Step 1: Enable Row Level Security (RLS)
ALTER TABLE "Data of Users" ENABLE ROW LEVEL SECURITY;

-- Step 2: Create policy to allow ALL authenticated users to insert data
CREATE POLICY "Allow authenticated users to insert" ON "Data of Users"
FOR INSERT TO authenticated
WITH CHECK (true);

-- Step 3: Create policy to allow ALL authenticated users to read data
CREATE POLICY "Users can read all data" ON "Data of Users"
FOR SELECT TO authenticated
USING (true);

-- Step 4: Grant necessary permissions
GRANT ALL ON "Data of Users" TO authenticated;
GRANT ALL ON "Data of Users" TO anon;
GRANT USAGE ON SCHEMA public TO authenticated;
GRANT USAGE ON SCHEMA public TO anon;

-- Verify the setup
SELECT schemaname, tablename, policyname, permissive, roles, cmd, qual, with_check
FROM pg_policies
WHERE tablename = 'Data of Users';

-- Also check table permissions
SELECT grantee, privilege_type
FROM information_schema.role_table_grants
WHERE table_name = 'Data of Users';
