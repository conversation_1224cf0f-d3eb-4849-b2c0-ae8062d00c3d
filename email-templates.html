<!-- Modern Email Templates for Zahoor -->
<!-- Copy these templates into your Supabase Email Templates -->

<!-- 1. CONFIRM SIGNUP EMAIL TEMPLATE -->
<!-- Use this for: Authentication > Email Templates > Confirm signup -->

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Welcome to Zahoor - Confirm Your Email</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #f8fafc 0%, #e0e7ef 100%);
            line-height: 1.6;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: #ffffff;
            border-radius: 16px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            margin-top: 40px;
            margin-bottom: 40px;
        }
        .header {
            background: linear-gradient(120deg, #3b82f6 0%, #60a5fa 100%);
            padding: 40px 30px;
            text-align: center;
            color: white;
        }
        .logo {
            font-size: 32px;
            font-weight: 700;
            margin-bottom: 10px;
            letter-spacing: 2px;
        }
        .leaf-icon {
            font-size: 24px;
            margin-left: 8px;
        }
        .subtitle {
            font-size: 16px;
            opacity: 0.9;
            margin: 0;
        }
        .content {
            padding: 40px 30px;
            text-align: center;
        }
        .welcome-title {
            font-size: 28px;
            color: #2d3748;
            margin-bottom: 16px;
            font-weight: 600;
        }
        .welcome-text {
            font-size: 16px;
            color: #4a5568;
            margin-bottom: 32px;
            line-height: 1.6;
        }
        .confirm-button {
            display: inline-block;
            background: linear-gradient(120deg, #3b82f6 0%, #60a5fa 100%);
            color: white;
            text-decoration: none;
            padding: 16px 32px;
            border-radius: 12px;
            font-weight: 600;
            font-size: 16px;
            margin: 20px 0;
            box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
            transition: transform 0.2s;
        }
        .confirm-button:hover {
            transform: translateY(-2px);
        }
        .footer {
            background: #f7fafc;
            padding: 30px;
            text-align: center;
            border-top: 1px solid #e2e8f0;
        }
        .footer-text {
            font-size: 14px;
            color: #718096;
            margin: 0;
        }
        .social-links {
            margin-top: 20px;
        }
        .social-link {
            display: inline-block;
            margin: 0 10px;
            color: #3b82f6;
            text-decoration: none;
        }
        @media (max-width: 600px) {
            .container {
                margin: 20px;
                border-radius: 12px;
            }
            .header, .content, .footer {
                padding: 30px 20px;
            }
            .welcome-title {
                font-size: 24px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">
                Zahoor <span class="leaf-icon">🍃</span>
            </div>
            <p class="subtitle">Elegant Modest Fashion</p>
        </div>
        
        <div class="content">
            <h1 class="welcome-title">Welcome to Zahoor!</h1>
            <p class="welcome-text">
                Thank you for joining our community of fashion-forward individuals. 
                We're excited to have you discover our collection of elegant and modest clothing.
            </p>
            <p class="welcome-text">
                To complete your registration and start shopping, please confirm your email address by clicking the button below:
            </p>
            
            <a href="{{ .ConfirmationURL }}" class="confirm-button">
                Confirm Your Email Address
            </a>
            
            <p style="font-size: 14px; color: #718096; margin-top: 30px;">
                If the button doesn't work, copy and paste this link into your browser:<br>
                <a href="{{ .ConfirmationURL }}" style="color: #3b82f6; word-break: break-all;">{{ .ConfirmationURL }}</a>
            </p>
        </div>
        
        <div class="footer">
            <p class="footer-text">
                This email was sent to {{ .Email }}. If you didn't create an account with Zahoor, you can safely ignore this email.
            </p>
            <div class="social-links">
                <a href="#" class="social-link">Website</a> |
                <a href="#" class="social-link">Instagram</a> |
                <a href="#" class="social-link">Contact Us</a>
            </div>
            <p class="footer-text" style="margin-top: 20px;">
                © 2025 Zahoor. All rights reserved.
            </p>
        </div>
    </div>
</body>
</html>

<!-- ================================ -->
<!-- 2. PASSWORD RESET EMAIL TEMPLATE -->
<!-- Use this for: Authentication > Email Templates > Reset password -->
<!-- ================================ -->

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reset Your Zahoor Password</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #f8fafc 0%, #e0e7ef 100%);
            line-height: 1.6;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: #ffffff;
            border-radius: 16px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            margin-top: 40px;
            margin-bottom: 40px;
        }
        .header {
            background: linear-gradient(120deg, #3b82f6 0%, #60a5fa 100%);
            padding: 40px 30px;
            text-align: center;
            color: white;
        }
        .logo {
            font-size: 32px;
            font-weight: 700;
            margin-bottom: 10px;
            letter-spacing: 2px;
        }
        .leaf-icon {
            font-size: 24px;
            margin-left: 8px;
        }
        .subtitle {
            font-size: 16px;
            opacity: 0.9;
            margin: 0;
        }
        .content {
            padding: 40px 30px;
            text-align: center;
        }
        .reset-title {
            font-size: 28px;
            color: #2d3748;
            margin-bottom: 16px;
            font-weight: 600;
        }
        .reset-text {
            font-size: 16px;
            color: #4a5568;
            margin-bottom: 32px;
            line-height: 1.6;
        }
        .reset-button {
            display: inline-block;
            background: linear-gradient(120deg, #3b82f6 0%, #60a5fa 100%);
            color: white;
            text-decoration: none;
            padding: 16px 32px;
            border-radius: 12px;
            font-weight: 600;
            font-size: 16px;
            margin: 20px 0;
            box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
            transition: transform 0.2s;
        }
        .reset-button:hover {
            transform: translateY(-2px);
        }
        .security-note {
            background: #fef7e7;
            border: 1px solid #f6cc4d;
            border-radius: 8px;
            padding: 16px;
            margin: 30px 0;
            font-size: 14px;
            color: #744210;
        }
        .footer {
            background: #f7fafc;
            padding: 30px;
            text-align: center;
            border-top: 1px solid #e2e8f0;
        }
        .footer-text {
            font-size: 14px;
            color: #718096;
            margin: 0;
        }
        @media (max-width: 600px) {
            .container {
                margin: 20px;
                border-radius: 12px;
            }
            .header, .content, .footer {
                padding: 30px 20px;
            }
            .reset-title {
                font-size: 24px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">
                Zahoor <span class="leaf-icon">🍃</span>
            </div>
            <p class="subtitle">Elegant Modest Fashion</p>
        </div>

        <div class="content">
            <h1 class="reset-title">Reset Your Password</h1>
            <p class="reset-text">
                We received a request to reset the password for your Zahoor account associated with {{ .Email }}.
            </p>
            <p class="reset-text">
                Click the button below to create a new password:
            </p>

            <a href="{{ .ConfirmationURL }}" class="reset-button">
                Reset My Password
            </a>

            <div class="security-note">
                <strong>🔒 Security Note:</strong> This link will expire in 24 hours for your security.
                If you didn't request this password reset, you can safely ignore this email.
            </div>

            <p style="font-size: 14px; color: #718096; margin-top: 30px;">
                If the button doesn't work, copy and paste this link into your browser:<br>
                <a href="{{ .ConfirmationURL }}" style="color: #3b82f6; word-break: break-all;">{{ .ConfirmationURL }}</a>
            </p>
        </div>

        <div class="footer">
            <p class="footer-text">
                This email was sent to {{ .Email }}. If you have any questions, please contact our support team.
            </p>
            <p class="footer-text" style="margin-top: 20px;">
                © 2025 Zahoor. All rights reserved.
            </p>
        </div>
    </div>
</body>
</html>
